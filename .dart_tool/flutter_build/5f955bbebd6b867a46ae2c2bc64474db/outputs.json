["F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\flutter_windows.dll", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\flutter_windows.dll.exp", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\flutter_windows.dll.lib", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\flutter_windows.dll.pdb", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\flutter_export.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\flutter_messenger.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\flutter_plugin_registrar.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\flutter_texture_registrar.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\flutter_windows.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\icudtl.dat", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\binary_messenger_impl.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\byte_buffer_streams.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\core_implementations.cc", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\engine_method_result.cc", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_engine.cc", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\flutter_view_controller.cc", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\basic_message_channel.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\binary_messenger.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\byte_streams.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\dart_project.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\encodable_value.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\engine_method_result.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_channel.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_sink.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\event_stream_handler_functions.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_engine.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\flutter_view_controller.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\message_codec.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_call.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_channel.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_codec.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\method_result_functions.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registrar_windows.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\plugin_registry.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_codec_serializer.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_message_codec.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\standard_method_codec.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\include\\flutter\\texture_registrar.h", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\plugin_registrar.cc", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\readme", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\standard_codec.cc", "F:\\code\\MaisVida\\windows\\flutter\\ephemeral\\cpp_client_wrapper\\texture_registrar_impl.h", "F:\\code\\MaisVida\\build\\flutter_assets\\kernel_blob.bin", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/1.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/10.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/11.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/12.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/13.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/14.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/15.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/16.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/17.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/18.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/19.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/2.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/20.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/21.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/22.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/23.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/24.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/25.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/26.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/27.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/28.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/29.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/3.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/30.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/31.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/32.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/33.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/34.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/35.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/36.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/37.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/38.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/39.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/4.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/40.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/41.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/42.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/43.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/44.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/45.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/46.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/47.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/48.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/49.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/5.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/50.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/6.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/7.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/8.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/9.png", "F:\\code\\MaisVida\\build\\flutter_assets\\assets/images/back.png", "F:\\code\\MaisVida\\build\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "F:\\code\\MaisVida\\build\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "F:\\code\\MaisVida\\build\\flutter_assets\\shaders/ink_sparkle.frag", "F:\\code\\MaisVida\\build\\flutter_assets\\AssetManifest.json", "F:\\code\\MaisVida\\build\\flutter_assets\\AssetManifest.bin", "F:\\code\\MaisVida\\build\\flutter_assets\\FontManifest.json", "F:\\code\\MaisVida\\build\\flutter_assets\\NOTICES.Z"]