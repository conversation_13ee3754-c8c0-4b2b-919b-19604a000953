F:\\code\\MaisVida\\.dart_tool\\flutter_build\\5f955bbebd6b867a46ae2c2bc64474db\\app.dill: F:\\code\\MaisVida\\lib\\main.dart F:\\code\\MaisVida\\.dart_tool\\flutter_build\\dart_plugin_registrant.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\dart_plugin_registrant.dart F:\\code\\flutter\\packages\\flutter\\lib\\material.dart F:\\code\\flutter\\packages\\flutter\\lib\\services.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\google_fonts.dart F:\\code\\MaisVida\\lib\\screens\\home_screen.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.4\\lib\\path_provider_android.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.2\\lib\\url_launcher_android.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\path_provider_foundation.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.2\\lib\\url_launcher_ios.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\path_provider_linux.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\lib\\url_launcher_linux.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\lib\\url_launcher_macos.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\path_provider_windows.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.3\\lib\\url_launcher_windows.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\about.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\action_buttons.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\action_chip.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\action_icons_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\adaptive_text_selection_toolbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\app.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\app_bar_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\arc.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\autocomplete.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\badge.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\badge_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\banner.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\banner_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_app_bar_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_navigation_bar_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\bottom_sheet_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\button_bar_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\button_style.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\button_style_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\button_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\calendar_date_picker.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\card.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\card_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_list_tile.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\checkbox_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\chip.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\chip_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\choice_chip.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\circle_avatar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\color_scheme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\colors.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\constants.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\curves.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\data_table.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_source.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\data_table_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\date.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\date_picker_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\debug.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\desktop_text_selection_toolbar_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\dialog.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\dialog_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\divider.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\divider_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\drawer.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_header.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\drawer_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\dropdown_menu_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\elevated_button_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\elevation_overlay.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\expand_icon.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_panel.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\expansion_tile_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\feedback.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\filled_button_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\filter_chip.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\flexible_space_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_location.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\floating_action_button_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\flutter_logo.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\grid_tile_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\icon_button_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\icons.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\ink_decoration.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\ink_highlight.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\ink_ripple.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\ink_sparkle.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\ink_splash.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\ink_well.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\input_border.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\input_chip.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\input_date_picker_form_field.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\input_decorator.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\list_tile_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\magnifier.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\material.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\material_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\material_localizations.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\material_state.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\material_state_mixin.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\menu_anchor.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\menu_bar_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\menu_button_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\menu_style.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\menu_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\mergeable_material.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\motion.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_bar_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_drawer_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\navigation_rail_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\no_splash.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\outlined_button_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\page.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\page_transitions_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\paginated_data_table.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\popup_menu_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\progress_indicator_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\radio.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\radio_list_tile.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\radio_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\range_slider.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\refresh_indicator.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\reorderable_list.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\scaffold.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\scrollbar_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\search.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\search_anchor.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\search_bar_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\search_view_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\segmented_button_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\selectable_text.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\selection_area.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\shadows.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\slider.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\slider_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\snack_bar_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\spell_check_suggestions_toolbar_layout_delegate.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\stepper.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\switch.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\switch_list_tile.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\switch_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\tab_bar_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\tab_controller.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\tab_indicator.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\tabs.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\text_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\text_button_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\text_field.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\text_form_field.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\text_selection_toolbar_text_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\text_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\theme_data.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\time.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\time_picker_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\toggle_buttons_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\toggleable.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\tooltip_visibility.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\typography.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\user_accounts_drawer_header.dart F:\\code\\flutter\\packages\\flutter\\lib\\widgets.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\asset_bundle.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\asset_manifest.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\autofill.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\binary_messenger.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\binding.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\browser_context_menu.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\clipboard.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\debug.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\deferred_component.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\flavor.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\font_loader.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\haptic_feedback.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\hardware_keyboard.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_inserted_content.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_key.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\keyboard_maps.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\live_text.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\message_codec.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\message_codecs.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_cursor.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\mouse_tracking.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\platform_channel.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\platform_views.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\process_text.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_android.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_fuchsia.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_ios.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_linux.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_macos.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_web.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\raw_keyboard_windows.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\restoration.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\service_extensions.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\spell_check.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\system_channels.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\system_chrome.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\system_navigator.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\system_sound.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\text_boundary.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\text_editing_delta.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\text_formatter.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\text_input.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\text_layout_metrics.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\undo_manager.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_base.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_a.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_b.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_c.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_d.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_e.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_f.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_g.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_h.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_i.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_j.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_k.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_l.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_m.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_n.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_o.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_p.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_q.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_r.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_s.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_t.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_u.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_v.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_w.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_x.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_y.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_parts\\part_z.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_staggered_animations-1.1.1\\lib\\flutter_staggered_animations.dart F:\\code\\MaisVida\\lib\\models\\fortune_card.dart F:\\code\\MaisVida\\lib\\widgets\\fortune_card_widget.dart F:\\code\\MaisVida\\lib\\screens\\about_screen.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\path_provider_platform_interface.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_android-2.2.4\\lib\\messages.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\foundation.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\link.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\url_launcher_platform_interface.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_android-6.3.2\\lib\\src\\messages.g.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_foundation-2.4.1\\lib\\messages.g.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_ios-6.3.2\\lib\\src\\messages.g.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\path_provider_linux.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_linux-3.2.1\\lib\\src\\messages.g.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_macos-3.2.2\\lib\\src\\messages.g.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\folders.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\path_provider_windows_real.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_windows-3.1.3\\lib\\src\\messages.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\scheduler.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\back_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\cupertino.dart F:\\code\\flutter\\packages\\flutter\\lib\\rendering.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\animated_icons_data.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\add_event.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\arrow_menu.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\close_menu.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\ellipsis_search.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\event_add.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\home_menu.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\list_view.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_arrow.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_close.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\menu_home.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\pause_play.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\play_pause.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\search_ellipsis.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\material\\animated_icons\\data\\view_list.g.dart F:\\code\\flutter\\packages\\flutter\\lib\\animation.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\vector_math_64.dart F:\\code\\flutter\\packages\\flutter\\lib\\gestures.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\material_color_utilities.dart F:\\code\\flutter\\packages\\flutter\\lib\\painting.dart F:\\code\\flutter\\packages\\flutter\\lib\\semantics.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\characters.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\actions.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\adapter.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_cross_fade.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_scroll_view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_size.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\animated_switcher.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\annotated_region.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\app.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\app_lifecycle_listener.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\async.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\autocomplete.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\autofill.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\automatic_keep_alive.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\banner.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\basic.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\binding.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\bottom_navigation_bar_item.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\color_filter.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\container.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_button_item.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\context_menu_controller.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\debug.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\decorated_sliver.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_selection_style.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\default_text_editing_shortcuts.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\desktop_text_selection_toolbar_layout_delegate.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\dismissible.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\display_feature_sub_screen.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\disposable_build_context.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\drag_target.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\draggable_scrollable_sheet.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\dual_transition_builder.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\editable_text.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\fade_in_image.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_manager.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_scope.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\focus_traversal.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\form.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\framework.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\gesture_detector.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\grid_paper.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\heroes.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_data.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\icon_theme_data.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\image.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_filter.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\image_icon.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\implicit_animations.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_model.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_notifier.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\inherited_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\interactive_viewer.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\keyboard_listener.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\layout_builder.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\list_wheel_scroll_view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\localizations.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\lookup_boundary.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\magnifier.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\media_query.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\modal_barrier.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigation_toolbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\navigator_pop_handler.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\nested_scroll_view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\notification_listener.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\orientation_builder.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\overflow_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\overlay.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\overscroll_indicator.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_storage.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\page_view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\pages.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\performance_overlay.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\placeholder.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_menu_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_selectable_region_context_menu.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\platform_view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\pop_scope.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\preferred_size.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\primary_scroll_controller.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\raw_keyboard_listener.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\reorderable_list.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\restoration_properties.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\router.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\routes.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\safe_area.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_activity.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_aware_image_provider.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_configuration.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_context.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_controller.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_delegate.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_metrics.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_notification_observer.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_physics.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_position_with_single_context.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_simulation.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scroll_view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollable_helpers.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\scrollbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\selectable_region.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\selection_container.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\semantics_debugger.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\service_extensions.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\shared_app_data.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\shortcuts.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\single_child_scroll_view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\size_changed_layout_notifier.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_fill.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_layout_builder.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_persistent_header.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_prototype_extent_list.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\sliver_varied_extent_list.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\slotted_render_object_widget.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\snapshot_widget.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\spacer.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\spell_check.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\status_transitions.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\table.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\tap_region.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\text.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_editing_intents.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_anchors.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\text_selection_toolbar_layout_delegate.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\texture.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\ticker_provider.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\title.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\transitions.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\tween_animation_builder.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_scroll_view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\two_dimensional_viewport.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\undo_history.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\unique_widget.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\value_listenable_builder.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\viewport.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\visibility.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_inspector.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\widget_span.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\will_pop_scope.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\services\\_background_isolate_binary_messenger_io.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\crypto.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\http.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\file_io_desktop_and_mobile.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_descriptor.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_family_with_variant.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\google_fonts-6.3.0\\lib\\src\\google_fonts_variant.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_staggered_animations-1.1.1\\lib\\src\\animation_configuration.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_staggered_animations-1.1.1\\lib\\src\\animation_limiter.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_staggered_animations-1.1.1\\lib\\src\\fade_in_animation.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_staggered_animations-1.1.1\\lib\\src\\flip_animation.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_staggered_animations-1.1.1\\lib\\src\\scale_animation.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_staggered_animations-1.1.1\\lib\\src\\slide_animation.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\url_launcher.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\plugin_platform_interface-2.1.8\\lib\\plugin_platform_interface.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\enums.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_platform_interface-2.1.2\\lib\\src\\method_channel_path_provider.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.11.0\\lib\\meta.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\annotations.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\assertions.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\basic_types.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\binding.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\bitfield.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\capabilities.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\change_notifier.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\collections.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\consolidate_response.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\constants.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\debug.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\diagnostics.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\isolates.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\key.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\licenses.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\memory_allocations.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\node.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\object.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\observer_list.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\persistent_hash_map.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\platform.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\print.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\serialization.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\service_extensions.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\stack_frame.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\synchronous_future.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\timeline.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\unicode.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\src\\types.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\src\\url_launcher_platform.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\path.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\xdg_directories-1.1.0\\lib\\xdg_directories.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\lib\\ffi.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\guid.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_windows-2.3.0\\lib\\src\\win32_wrappers.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\scheduler\\binding.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\scheduler\\debug.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\scheduler\\priority.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\scheduler\\service_extensions.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\scheduler\\ticker.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\activity_indicator.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\adaptive_text_selection_toolbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\app.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\bottom_tab_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\checkbox.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\colors.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\constants.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\context_menu_action.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\date_picker.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\debug.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\desktop_text_selection_toolbar_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\dialog.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_row.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\form_section.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icon_theme_data.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\icons.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\interface_level.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_section.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\list_tile.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\localizations.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\magnifier.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\nav_bar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\page_scaffold.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\picker.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\radio.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\refresh.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\route.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\scrollbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\search_field.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\segmented_control.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\slider.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\sliding_segmented_control.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\spell_check_suggestions_toolbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\switch.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_scaffold.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\tab_view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_field.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_form_field_row.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_selection_toolbar_button.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\text_theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\theme.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\thumb_painter.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\animated_size.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\binding.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\box.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_layout.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\custom_paint.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\debug_overflow_indicator.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\decorated_sliver.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\editable.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\error.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\flex.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\flow.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\image.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\layer.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\layout_helper.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_body.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\list_wheel_viewport.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\mouse_tracker.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\object.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\paragraph.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\performance_overlay.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\platform_view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_box.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\proxy_sliver.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\rotated_box.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\selection.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\service_extensions.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\shifted_box.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fill.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_fixed_extent_list.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_grid.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_group.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_list.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_multi_box_adaptor.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_padding.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\sliver_persistent_header.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\stack.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\table.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\table_border.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\texture.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\tweens.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\view.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\viewport_offset.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\rendering\\wrap.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\animation\\animation.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_controller.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\animation\\animation_style.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\animation\\animations.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\animation\\curves.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\animation\\listener_helpers.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\animation\\tween.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\animation\\tween_sequence.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb2.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\aabb3.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\colors.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\constants.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\error_helpers.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\frustum.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\intersection_result.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix2.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix3.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\matrix4.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\noise.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\obb3.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\opengl.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\plane.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quad.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\quaternion.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\ray.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\sphere.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\triangle.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\utilities.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector2.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector3.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\vector_math-2.1.4\\lib\\src\\vector_math_64\\vector4.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\arena.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\binding.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\constants.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\converter.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\debug.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\drag_details.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\eager.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\events.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\force_press.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\gesture_settings.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\hit_test.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\long_press.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\lsq_solver.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\monodrag.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\multidrag.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\multitap.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_router.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\pointer_signal_resolver.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\recognizer.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\resampler.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\scale.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\tap_and_drag.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\team.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\gestures\\velocity_tracker.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\blend\\blend.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\contrast\\contrast.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\dislike\\dislike_analyzer.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\dynamiccolor\\dynamic_color.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\dynamiccolor\\material_dynamic_colors.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\hct\\cam16.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\hct\\hct.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\hct\\viewing_conditions.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\palettes\\core_palette.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\palettes\\tonal_palette.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\quantizer.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\quantizer_celebi.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\quantizer_map.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\quantizer_wsmeans.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\quantizer_wu.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\dynamic_scheme.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_content.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_expressive.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_fidelity.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_monochrome.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_neutral.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_tonal_spot.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\scheme_vibrant.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\scheme\\variant.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\score\\score.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\temperature\\temperature_cache.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\utils\\color_utils.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\utils\\math_utils.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\utils\\string_utils.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\alignment.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\basic_types.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\beveled_rectangle_border.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\binding.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\border_radius.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\borders.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\box_border.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\box_decoration.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\box_fit.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\box_shadow.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\circle_border.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\clip.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\colors.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\continuous_rectangle_border.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\debug.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\decoration_image.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\edge_insets.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\flutter_logo.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\fractional_offset.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\geometry.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\gradient.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\image_cache.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\image_decoder.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\image_provider.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\image_resolution.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\image_stream.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\inline_span.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\linear_border.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\matrix_utils.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\notched_shapes.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\oval_border.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\paint_utilities.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\placeholder_span.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\rounded_rectangle_border.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\shader_warm_up.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\shape_decoration.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\stadium_border.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\star_border.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\strut_style.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\text_painter.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\text_scaler.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\text_span.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\text_style.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\semantics\\binding.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\semantics\\debug.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_event.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\semantics\\semantics_service.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\characters.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\extensions.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\constants.dart F:\\code\\flutter\\packages\\flutter\\lib\\physics.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\_platform_selectable_region_context_menu_io.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\widgets\\_html_element_view_io.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\collection.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\meta-1.11.0\\lib\\meta_meta.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\digest.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\hash.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\hmac.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\md5.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\sha1.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\sha256.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\sha512.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\client.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\exception.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\request.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\response.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\streamed_request.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\base_client.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\base_request.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\base_response.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\byte_stream.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\multipart_file.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\multipart_request.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\streamed_response.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider-2.1.4\\lib\\path_provider.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_staggered_animations-1.1.1\\lib\\src\\animation_configurator.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\legacy_api.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\types.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\url_launcher_uri.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\platform.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\_bitfield_io.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\_capabilities_io.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\_isolates_io.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\_platform_io.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\foundation\\_timeline_io.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher_platform_interface-2.3.2\\lib\\method_channel_url_launcher.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\context.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\path_exception.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\path_map.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\path_set.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path_provider_linux-2.2.1\\lib\\src\\get_application_id_real.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\allocation.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\arena.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\utf16.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\ffi-2.1.3\\lib\\src\\utf8.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\cupertino\\toggleable.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\dynamiccolor\\src\\contrast_curve.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\dynamiccolor\\src\\tone_delta_pair.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\hct\\src\\hct_solver.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\src\\point_provider_lab.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\material_color_utilities-0.8.0\\lib\\quantize\\src\\point_provider.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\painting\\_network_image_io.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\characters_impl.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\physics\\clamped_simulation.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\physics\\friction_simulation.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\physics\\gravity_simulation.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\physics\\simulation.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\physics\\spring_simulation.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\physics\\tolerance.dart F:\\code\\flutter\\packages\\flutter\\lib\\src\\physics\\utils.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\algorithms.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\boollist.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\canonicalized_map.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_iterable.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_list.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_map.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\comparators.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\equality.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\equality_map.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\equality_set.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\functions.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\iterable_extensions.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\iterable_zip.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\list_extensions.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\priority_queue.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\queue_list.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\union_set.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\union_set_controller.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\unmodifiable_wrappers.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\wrappers.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\digest_sink.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\hash_sink.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\utils.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\crypto-3.0.3\\lib\\src\\sha512_fastsinks.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\io_client.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\http_parser.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\utils.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\multipart_file_io.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\boundary_characters.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\flutter_staggered_animations-1.1.1\\lib\\src\\animation_executor.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\url_launcher_string.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\type_conversion.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\local_platform.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\interface\\platform.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\platform-3.1.6\\lib\\src\\testing\\fake_platform.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\characters.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\internal_style.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\parsed_path.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style\\posix.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style\\url.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\style\\windows.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\grapheme_clusters\\table.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\grapheme_clusters\\constants.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\characters-1.3.0\\lib\\src\\grapheme_clusters\\breaks.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\utils.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\combined_wrappers\\combined_iterator.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\collection-1.18.0\\lib\\src\\empty_unmodifiable_set.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.3.2\\lib\\typed_data.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http-1.2.2\\lib\\src\\io_streamed_response.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\authentication_challenge.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\case_insensitive_map.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\chunked_coding.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\http_date.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\media_type.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\url_launcher-6.3.1\\lib\\src\\url_launcher_string.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\path-1.9.0\\lib\\src\\utils.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.3.2\\lib\\src\\typed_queue.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.3.2\\lib\\typed_buffers.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\string_scanner.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\scan.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\utils.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\chunked_coding\\decoder.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\chunked_coding\\encoder.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\typed_data-1.3.2\\lib\\src\\typed_buffer.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\exception.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\line_scanner.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\span_scanner.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\string_scanner.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\source_span.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\http_parser-4.0.2\\lib\\src\\chunked_coding\\charcodes.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\charcode.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\utils.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\eager_span_scanner.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\string_scanner-1.2.0\\lib\\src\\relative_span_scanner.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\file.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\location.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\location_mixin.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\span.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\span_exception.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\span_mixin.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\span_with_context.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\lib\\term_glyph.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\highlighter.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\utils.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\lib\\src\\generated\\ascii_glyph_set.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\lib\\src\\generated\\glyph_set.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\lib\\src\\generated\\unicode_glyph_set.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\term_glyph-1.2.1\\lib\\src\\generated\\top_level.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\charcode.dart C:\\Users\\<USER>\\AppData\\Local\\Pub\\Cache\\hosted\\pub.dev\\source_span-1.10.0\\lib\\src\\colors.dart
