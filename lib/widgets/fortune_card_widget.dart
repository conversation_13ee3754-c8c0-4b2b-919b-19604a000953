import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'dart:math';
import '../models/fortune_card.dart';

class FortuneCardWidget extends StatefulWidget {
  final FortuneCard card;
  final bool isSelected;
  final bool isRevealed;
  final VoidCallback onTap;
  final VoidCallback onDoubleTap;
  final AnimationController sparkleController;
  final AnimationController floatingController;

  const FortuneCardWidget({
    super.key,
    required this.card,
    required this.isSelected,
    required this.isRevealed,
    required this.onTap,
    required this.onDoubleTap,
    required this.sparkleController,
    required this.floatingController,
  });

  @override
  State<FortuneCardWidget> createState() => _FortuneCardWidgetState();
}

class _FortuneCardWidgetState extends State<FortuneCardWidget>
    with TickerProviderStateMixin {
  late AnimationController _flipController;
  late Animation<double> _flipAnimation;
  bool _wasRevealed = false;

  @override
  void initState() {
    super.initState();
    _flipController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _flipAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _flipController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void didUpdateWidget(FortuneCardWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Trigger flip animation when card is revealed
    if (widget.isRevealed && !_wasRevealed) {
      _flipController.forward();
      _wasRevealed = true;
    } else if (!widget.isRevealed && _wasRevealed) {
      _flipController.reverse();
      _wasRevealed = false;
    }
  }

  @override
  void dispose() {
    _flipController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
        animation: Listenable.merge([
          widget.sparkleController,
          widget.floatingController,
          _flipController,
        ]),
        builder: (context, child) {
          return Transform.translate(
            offset: widget.isSelected
                ? Offset(0, sin(widget.floatingController.value * 2 * pi) * 3)
                : Offset.zero,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 500),
              curve: Curves.easeInOut,
              width: 160, // Fixed card width
              height: 240, // Fixed card height for proper aspect ratio
              transform: Matrix4.identity()
                ..scale(widget.isSelected ? 1.05 : 1.0), // Reduced scale effect
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: widget.isSelected
                        ? Theme.of(context).colorScheme.secondary.withOpacity(0.6)
                        : Colors.black.withOpacity(0.4),
                    blurRadius: widget.isSelected ? 20 : 8,
                    spreadRadius: widget.isSelected ? 3 : 1,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.identity()
                    ..setEntry(3, 2, 0.001)
                    ..rotateY(_flipAnimation.value * pi),
                  child: _flipAnimation.value <= 0.5
                      ? _buildCardBack()
                      : Transform(
                          alignment: Alignment.center,
                          transform: Matrix4.identity()..rotateY(pi),
                          child: _buildCardFace(),
                        ),
                ),
              ),
            ),
          );
        },
    );
  }

  Widget _buildCardBack() {
    return Stack(
      children: [
        // Card back image
        Container(
          width: 160,
          height: 240,
          child: Image.asset(
            FortuneCard.backImagePath,
            fit: BoxFit.cover, // Changed to cover for better card appearance
            errorBuilder: (context, error, stackTrace) {
              print('Error loading back image: $error');
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      Theme.of(context).colorScheme.secondary,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        size: 60,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'CARTA VERSO',
                        style: GoogleFonts.crimsonText(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        
        // Sparkle effect for selected card
        if (widget.isSelected)
          AnimatedBuilder(
            animation: widget.sparkleController,
            builder: (context, child) {
              return Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withOpacity(
                          0.2 * sin(widget.sparkleController.value * 2 * pi).abs(),
                        ),
                        Colors.transparent,
                        Theme.of(context).colorScheme.secondary.withOpacity(
                          0.2 * cos(widget.sparkleController.value * 2 * pi).abs(),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        
        // Tap indicator for selected cards
        if (widget.isSelected && !widget.isRevealed)
          Center(
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 6,
              ),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.8),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: Theme.of(context).colorScheme.secondary,
                  width: 2,
                ),
              ),
              child: Text(
                'Toque 2x para revelar',
                style: GoogleFonts.crimsonText(
                  fontSize: 10,
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildCardFace() {
    return Stack(
      children: [
        // Card face image
        Container(
          width: 160,
          height: 240,
          child: Image.asset(
            widget.card.faceImagePath,
            fit: BoxFit.cover, // Changed to cover for better card appearance
            errorBuilder: (context, error, stackTrace) {
              print('Error loading face image: $error');
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.secondary,
                      Theme.of(context).colorScheme.tertiary,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        size: 40,
                        color: Colors.white,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'CARTA ${widget.card.id}',
                        style: GoogleFonts.crimsonText(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'FACE',
                        style: GoogleFonts.crimsonText(
                          fontSize: 12,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        
        // Card number in top right corner (only on face)
        Positioned(
          top: 8,
          right: 8,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 6,
              vertical: 3,
            ),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.tertiary,
                width: 1,
              ),
            ),
            child: Text(
              '${widget.card.id}',
              style: GoogleFonts.crimsonText(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.tertiary,
              ),
            ),
          ),
        ),
      ],
    );
  }
}