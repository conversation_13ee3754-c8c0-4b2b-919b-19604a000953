^F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\CMAKEFILES\CB614EA8380C9F098ED243D1D9BCEC4A\FLUTTER_WINDOWS.DLL.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=F:\code\flutter PROJECT_DIR=F:\code\MaisVida FLUTTER_ROOT=F:\code\flutter FLUTTER_EPHEMERAL_DIR=F:\code\MaisVida\windows\flutter\ephemeral PROJECT_DIR=F:\code\MaisVida FLUTTER_TARGET=F:\code\MaisVida\lib\main.dart DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC8wNDgxN2M5OWM5ZmQ0OTU2ZjI3NTA1MjA0ZjdlMzQ0MzM1ODEwYWVkLw== DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=F:\code\MaisVida\.dart_tool\package_config.json F:/code/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\CMAKEFILES\E851D9F44A7186DCBF4DC80678254977\FLUTTER_ASSEMBLE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^F:\CODE\MAISVIDA\WINDOWS\FLUTTER\CMAKELISTS.TXT
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SF:/code/MaisVida/windows -BF:/code/MaisVida/build/windows/x64 --check-stamp-file F:/code/MaisVida/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
