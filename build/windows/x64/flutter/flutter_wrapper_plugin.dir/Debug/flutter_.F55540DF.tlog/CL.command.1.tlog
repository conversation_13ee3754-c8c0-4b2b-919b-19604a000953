^F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\CORE_IMPLEMENTATIONS.CC
/c /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"FLUTTER_WRAPPER_PLUGIN.DIR\DEBUG\\" /Fd"F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\FLUTTER\DEBUG\FLUTTER_WRAPPER_PLUGIN.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\CORE_IMPLEMENTATIONS.CC
^F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\STANDARD_CODEC.CC
/c /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"FLUTTER_WRAPPER_PLUGIN.DIR\DEBUG\\" /Fd"F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\FLUTTER\DEBUG\FLUTTER_WRAPPER_PLUGIN.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\STANDARD_CODEC.CC
^F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\PLUGIN_REGISTRAR.CC
/c /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"FLUTTER_WRAPPER_PLUGIN.DIR\DEBUG\\" /Fd"F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\FLUTTER\DEBUG\FLUTTER_WRAPPER_PLUGIN.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\PLUGIN_REGISTRAR.CC
