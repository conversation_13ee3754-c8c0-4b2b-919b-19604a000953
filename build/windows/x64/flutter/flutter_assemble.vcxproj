<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Profile|x64">
      <Configuration>Profile</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B4FB687E-**************-A7FDB2FA02A1}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>flutter_assemble</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="F:\code\MaisVida\build\windows\x64\CMakeFiles\cb614ea8380c9f098ed243d1d9bcec4a\flutter_windows.dll.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Generating F:/code/MaisVida/windows/flutter/ephemeral/flutter_windows.dll, F:/code/MaisVida/windows/flutter/ephemeral/flutter_export.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_windows.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_messenger.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_plugin_registrar.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_texture_registrar.h, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=F:\code\flutter PROJECT_DIR=F:\code\MaisVida FLUTTER_ROOT=F:\code\flutter FLUTTER_EPHEMERAL_DIR=F:\code\MaisVida\windows\flutter\ephemeral PROJECT_DIR=F:\code\MaisVida FLUTTER_TARGET=F:\code\MaisVida\lib\main.dart DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC8wNDgxN2M5OWM5ZmQ0OTU2ZjI3NTA1MjA0ZjdlMzQ0MzM1ODEwYWVkLw== DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=F:\code\MaisVida\.dart_tool\package_config.json F:/code/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Debug
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.dll;F:\code\MaisVida\windows\flutter\ephemeral\flutter_export.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_messenger.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;F:\code\MaisVida\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Generating F:/code/MaisVida/windows/flutter/ephemeral/flutter_windows.dll, F:/code/MaisVida/windows/flutter/ephemeral/flutter_export.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_windows.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_messenger.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_plugin_registrar.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_texture_registrar.h, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=F:\code\flutter PROJECT_DIR=F:\code\MaisVida FLUTTER_ROOT=F:\code\flutter FLUTTER_EPHEMERAL_DIR=F:\code\MaisVida\windows\flutter\ephemeral PROJECT_DIR=F:\code\MaisVida FLUTTER_TARGET=F:\code\MaisVida\lib\main.dart DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC8wNDgxN2M5OWM5ZmQ0OTU2ZjI3NTA1MjA0ZjdlMzQ0MzM1ODEwYWVkLw== DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=F:\code\MaisVida\.dart_tool\package_config.json F:/code/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Profile
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.dll;F:\code\MaisVida\windows\flutter\ephemeral\flutter_export.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_messenger.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;F:\code\MaisVida\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Generating F:/code/MaisVida/windows/flutter/ephemeral/flutter_windows.dll, F:/code/MaisVida/windows/flutter/ephemeral/flutter_export.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_windows.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_messenger.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_plugin_registrar.h, F:/code/MaisVida/windows/flutter/ephemeral/flutter_texture_registrar.h, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/core_implementations.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/standard_codec.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/plugin_registrar.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/flutter_engine.cc, F:/code/MaisVida/windows/flutter/ephemeral/cpp_client_wrapper/flutter_view_controller.cc, _phony_</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -E env FLUTTER_ROOT=F:\code\flutter PROJECT_DIR=F:\code\MaisVida FLUTTER_ROOT=F:\code\flutter FLUTTER_EPHEMERAL_DIR=F:\code\MaisVida\windows\flutter\ephemeral PROJECT_DIR=F:\code\MaisVida FLUTTER_TARGET=F:\code\MaisVida\lib\main.dart DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC8wNDgxN2M5OWM5ZmQ0OTU2ZjI3NTA1MjA0ZjdlMzQ0MzM1ODEwYWVkLw== DART_OBFUSCATION=false TRACK_WIDGET_CREATION=true TREE_SHAKE_ICONS=false PACKAGE_CONFIG=F:\code\MaisVida\.dart_tool\package_config.json F:/code/flutter/packages/flutter_tools/bin/tool_backend.bat windows-x64 Release
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.dll;F:\code\MaisVida\windows\flutter\ephemeral\flutter_export.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_messenger.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;F:\code\MaisVida\build\windows\x64\flutter\_phony_</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\code\MaisVida\build\windows\x64\CMakeFiles\e851d9f44a7186dcbf4dc80678254977\flutter_assemble.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.dll;F:\code\MaisVida\windows\flutter\ephemeral\flutter_export.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_messenger.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\code\MaisVida\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.dll;F:\code\MaisVida\windows\flutter\ephemeral\flutter_export.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_messenger.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">F:\code\MaisVida\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</VerifyInputsAndOutputsExist>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.dll;F:\code\MaisVida\windows\flutter\ephemeral\flutter_export.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_windows.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_messenger.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_plugin_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\flutter_texture_registrar.h;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\core_implementations.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\standard_codec.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\plugin_registrar.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_engine.cc;F:\code\MaisVida\windows\flutter\ephemeral\cpp_client_wrapper\flutter_view_controller.cc;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\code\MaisVida\build\windows\x64\flutter\CMakeFiles\flutter_assemble</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\code\MaisVida\windows\flutter\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule F:/code/MaisVida/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SF:/code/MaisVida/windows -BF:/code/MaisVida/build/windows/x64 --check-stamp-file F:/code/MaisVida/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\code\MaisVida\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">F:\code\MaisVida\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">Building Custom Rule F:/code/MaisVida/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SF:/code/MaisVida/windows -BF:/code/MaisVida/build/windows/x64 --check-stamp-file F:/code/MaisVida/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">F:\code\MaisVida\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">F:\code\MaisVida\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Profile|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule F:/code/MaisVida/windows/flutter/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SF:/code/MaisVida/windows -BF:/code/MaisVida/build/windows/x64 --check-stamp-file F:/code/MaisVida/build/windows/x64/flutter/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\code\MaisVida\windows\flutter\ephemeral\generated_config.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">F:\code\MaisVida\build\windows\x64\flutter\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="F:\code\MaisVida\build\windows\x64\flutter\CMakeFiles\flutter_assemble">
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="F:\code\MaisVida\build\windows\x64\ZERO_CHECK.vcxproj">
      <Project>{C458E203-39AB-368C-A991-89809C7A5FB5}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>