^F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\URL_LAUNCHER_WINDOWS.CPP
/c /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D FLUTTER_PLUGIN_IMPL /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /D url_launcher_windows_plugin_EXPORTS /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"URL_LAUNCHER_WINDOWS_PLUGIN.DIR\DEBUG\\" /Fd"URL_LAUNCHER_WINDOWS_PLUGIN.DIR\DEBUG\VC143.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\URL_LAUNCHER_WINDOWS.CPP
^F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\MESSAGES.G.CPP
/c /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D FLUTTER_PLUGIN_IMPL /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /D url_launcher_windows_plugin_EXPORTS /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"URL_LAUNCHER_WINDOWS_PLUGIN.DIR\DEBUG\\" /Fd"URL_LAUNCHER_WINDOWS_PLUGIN.DIR\DEBUG\VC143.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\MESSAGES.G.CPP
^F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\SYSTEM_APIS.CPP
/c /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D FLUTTER_PLUGIN_IMPL /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /D url_launcher_windows_plugin_EXPORTS /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"URL_LAUNCHER_WINDOWS_PLUGIN.DIR\DEBUG\\" /Fd"URL_LAUNCHER_WINDOWS_PLUGIN.DIR\DEBUG\VC143.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\SYSTEM_APIS.CPP
^F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\URL_LAUNCHER_PLUGIN.CPP
/c /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _WINDLL /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D FLUTTER_PLUGIN_IMPL /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /D url_launcher_windows_plugin_EXPORTS /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"URL_LAUNCHER_WINDOWS_PLUGIN.DIR\DEBUG\\" /Fd"URL_LAUNCHER_WINDOWS_PLUGIN.DIR\DEBUG\VC143.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\URL_LAUNCHER_PLUGIN.CPP
