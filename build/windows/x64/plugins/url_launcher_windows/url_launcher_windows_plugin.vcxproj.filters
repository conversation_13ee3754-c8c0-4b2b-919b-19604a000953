<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="F:\code\MaisVida\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\url_launcher_windows.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\code\MaisVida\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\messages.g.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\code\MaisVida\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\system_apis.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="F:\code\MaisVida\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\url_launcher_plugin.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="F:\code\MaisVida\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\include\url_launcher_windows\url_launcher_windows.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\code\MaisVida\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\messages.g.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\code\MaisVida\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\system_apis.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="F:\code\MaisVida\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\url_launcher_plugin.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="F:\code\MaisVida\windows\flutter\ephemeral\.plugin_symlinks\url_launcher_windows\windows\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="Header Files">
      <UniqueIdentifier>{3DDF5E1D-B202-3FAF-9649-4FB82454F88B}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{58DED1C3-4E0B-3DEF-BB42-09BF83889A9B}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
