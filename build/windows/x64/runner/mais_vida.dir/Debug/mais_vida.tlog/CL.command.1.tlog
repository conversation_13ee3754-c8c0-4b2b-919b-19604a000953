^F:\CODE\MAISVIDA\WINDOWS\RUNNER\FLUTTER_WINDOW.CPP
/c /IF:\CODE\MAISVIDA\WINDOWS /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\INCLUDE /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D "FLUTTER_VERSION=\"1.0.0+1\"" /D FLUTTER_VERSION_MAJOR=1 /D FLUTTER_VERSION_MINOR=0 /D FLUTTER_VERSION_PATCH=0 /D FLUTTER_VERSION_BUILD=1 /D NOMINMAX /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"MAIS_VIDA.DIR\DEBUG\\" /Fd"MAIS_VIDA.DIR\DEBUG\VC143.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\RUNNER\FLUTTER_WINDOW.CPP
^F:\CODE\MAISVIDA\WINDOWS\RUNNER\MAIN.CPP
/c /IF:\CODE\MAISVIDA\WINDOWS /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\INCLUDE /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D "FLUTTER_VERSION=\"1.0.0+1\"" /D FLUTTER_VERSION_MAJOR=1 /D FLUTTER_VERSION_MINOR=0 /D FLUTTER_VERSION_PATCH=0 /D FLUTTER_VERSION_BUILD=1 /D NOMINMAX /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"MAIS_VIDA.DIR\DEBUG\\" /Fd"MAIS_VIDA.DIR\DEBUG\VC143.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\RUNNER\MAIN.CPP
^F:\CODE\MAISVIDA\WINDOWS\RUNNER\UTILS.CPP
/c /IF:\CODE\MAISVIDA\WINDOWS /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\INCLUDE /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D "FLUTTER_VERSION=\"1.0.0+1\"" /D FLUTTER_VERSION_MAJOR=1 /D FLUTTER_VERSION_MINOR=0 /D FLUTTER_VERSION_PATCH=0 /D FLUTTER_VERSION_BUILD=1 /D NOMINMAX /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"MAIS_VIDA.DIR\DEBUG\\" /Fd"MAIS_VIDA.DIR\DEBUG\VC143.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\RUNNER\UTILS.CPP
^F:\CODE\MAISVIDA\WINDOWS\RUNNER\WIN32_WINDOW.CPP
/c /IF:\CODE\MAISVIDA\WINDOWS /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\INCLUDE /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D "FLUTTER_VERSION=\"1.0.0+1\"" /D FLUTTER_VERSION_MAJOR=1 /D FLUTTER_VERSION_MINOR=0 /D FLUTTER_VERSION_PATCH=0 /D FLUTTER_VERSION_BUILD=1 /D NOMINMAX /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"MAIS_VIDA.DIR\DEBUG\\" /Fd"MAIS_VIDA.DIR\DEBUG\VC143.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\RUNNER\WIN32_WINDOW.CPP
^F:\CODE\MAISVIDA\WINDOWS\FLUTTER\GENERATED_PLUGIN_REGISTRANT.CC
/c /IF:\CODE\MAISVIDA\WINDOWS /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\CPP_CLIENT_WRAPPER\INCLUDE /IF:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\.PLUGIN_SYMLINKS\URL_LAUNCHER_WINDOWS\WINDOWS\INCLUDE /Zi /nologo /W4 /WX /diagnostics:column /Od /Ob0 /D _UNICODE /D UNICODE /D WIN32 /D _WINDOWS /D _HAS_EXCEPTIONS=0 /D _DEBUG /D "FLUTTER_VERSION=\"1.0.0+1\"" /D FLUTTER_VERSION_MAJOR=1 /D FLUTTER_VERSION_MINOR=0 /D FLUTTER_VERSION_PATCH=0 /D FLUTTER_VERSION_BUILD=1 /D NOMINMAX /D UNICODE /D _UNICODE /D "CMAKE_INTDIR=\"Debug\"" /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"MAIS_VIDA.DIR\DEBUG\\" /Fd"MAIS_VIDA.DIR\DEBUG\VC143.PDB" /external:W4 /Gd /TP /wd4100 F:\CODE\MAISVIDA\WINDOWS\FLUTTER\GENERATED_PLUGIN_REGISTRANT.CC
