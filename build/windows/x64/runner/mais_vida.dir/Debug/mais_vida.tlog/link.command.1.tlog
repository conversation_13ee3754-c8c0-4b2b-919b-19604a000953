^F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\RUNNER\MAIS_VIDA.DIR\DEBUG\FLUTTER_WINDOW.OBJ|F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\RUNNER\MAIS_VIDA.DIR\DEBUG\GENERATED_PLUGIN_REGISTRANT.OBJ|F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\RUNNER\MAIS_VIDA.DIR\DEBUG\MAIN.OBJ|F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\RUNNER\MAIS_VIDA.DIR\DEBUG\RUNNER.RES|F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\RUNNER\MAIS_VIDA.DIR\DEBUG\UTILS.OBJ|F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\RUNNER\MAIS_VIDA.DIR\DEBUG\WIN32_WINDOW.OBJ
/OUT:"F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\RUNNER\DEBUG\MAIS_VIDA.EXE" /INCREMENTAL /ILK:"MAIS_VIDA.DIR\DEBUG\MAIS_VIDA.ILK" /NOLOGO ..\FLUTTER\DEBUG\FLUTTER_WRAPPER_APP.LIB DWMAPI.LIB ..\PLUGINS\URL_LAUNCHER_WINDOWS\DEBUG\URL_LAUNCHER_WINDOWS_PLUGIN.LIB F:\CODE\MAISVIDA\WINDOWS\FLUTTER\EPHEMERAL\FLUTTER_WINDOWS.DLL.LIB KERNEL32.LIB USER32.LIB GDI32.LIB WINSPOOL.LIB SHELL32.LIB OLE32.LIB OLEAUT32.LIB UUID.LIB COMDLG32.LIB ADVAPI32.LIB /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /manifestinput:F:\CODE\MAISVIDA\WINDOWS\RUNNER\RUNNER.EXE.MANIFEST /DEBUG /PDB:"F:/CODE/MAISVIDA/BUILD/WINDOWS/X64/RUNNER/DEBUG/MAIS_VIDA.PDB" /SUBSYSTEM:WINDOWS /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"F:/CODE/MAISVIDA/BUILD/WINDOWS/X64/RUNNER/DEBUG/MAIS_VIDA.LIB" /MACHINE:X64  /machine:x64 MAIS_VIDA.DIR\DEBUG\RUNNER.RES
MAIS_VIDA.DIR\DEBUG\FLUTTER_WINDOW.OBJ
MAIS_VIDA.DIR\DEBUG\MAIN.OBJ
MAIS_VIDA.DIR\DEBUG\UTILS.OBJ
MAIS_VIDA.DIR\DEBUG\WIN32_WINDOW.OBJ
MAIS_VIDA.DIR\DEBUG\GENERATED_PLUGIN_REGISTRANT.OBJ
