^F:\CODE\MAISVIDA\BUILD\WINDOWS\X64\CMAKEFILES\48910385DDB5D427F365928A6E9E504C\GENERATE.STAMP.RULE
setlocal
"C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SF:/code/MaisVida/windows -BF:/code/MaisVida/build/windows/x64 --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file F:/code/MaisVida/build/windows/x64/mais_vida.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
